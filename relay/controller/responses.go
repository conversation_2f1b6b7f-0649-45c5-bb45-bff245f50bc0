package controller

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/middleware"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common/client"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/adaptor/openai"
	"github.com/songquanpeng/one-api/relay/billing"
	billingratio "github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/meta"
	relayModel "github.com/songquanpeng/one-api/relay/model"
)

// RelayResponsesHelper 处理 POST /v1/responses 请求
func RelayResponsesHelper(c *gin.Context) (*relayModel.ErrorWithStatusCode, bool, meta.Meta) {
	ctx := c.Request.Context()
	meta := meta.GetByContext(c)

	// 读取请求体
	requestBody, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return openai.ErrorWrapper(err, "read_request_body_failed", http.StatusBadRequest), false, *meta
	}
	c.Request.Body = io.NopCloser(bytes.NewReader(requestBody)) // 重置请求体，以便后续使用

	// 保存请求内容用于详细日志记录
	meta.DetailPrompt = string(requestBody)

	// 解析请求
	request, err := getAndValidateTextRequest(c, meta.Mode)
	if err != nil {
		logger.Errorf(ctx, "getAndValidateTextRequest failed: %s", err.Error())
		return openai.ErrorWrapper(err, "invalid_text_request", http.StatusBadRequest), false, *meta
	}

	// 设置模型信息
	meta.OriginModelName = request.Model
	meta.ActualModelName = request.Model
	meta.IsStream = request.Stream
	meta.RequestModel = request.Model

	// 获取模型倍率和分组倍率
	modelRatio := billingratio.GetModelRatio(lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(request.Model), meta.ChannelType)
	meta.UpstreamModelRatio = modelRatio

	// 获取用户个性化费率
	userModelRatio, ok, _, err := model.CacheGetUserModelRatio(meta.UserId, lo.If(meta.OriginalModelPricing, meta.OriginModelName).Else(request.Model))
	if ok {
		modelRatio = userModelRatio
	}

	groupRatio := billingratio.GetGroupRatio(meta.Group)
	ratio := modelRatio * groupRatio

	// 计算预扣费额度
	topupConvertRatio, userDiscount := billing.GetTopupConvertRatioByMeta(meta)

	// 预估 token 数量
	estimatedTokens := estimateTokensForResponses(request)
	meta.PromptTokens = estimatedTokens

	// 预扣费
	var preConsumedQuota int64
	var bizErr *relayModel.ErrorWithStatusCode

	if meta.IsPackagePlan {
		preConsumedQuota, bizErr = preConsumePackagePlanQuota(ctx, request, estimatedTokens, ratio, meta)
	} else {
		// 转换充值率
		preConsumedQuota, bizErr = preConsumeQuota(ctx, request, estimatedTokens, ratio*topupConvertRatio*userDiscount, meta)
	}

	if bizErr != nil {
		logger.Warnf(ctx, "preConsumeQuota failed: %+v", *bizErr)
		return bizErr, false, *meta
	}

	// 记录开始时间
	meta.StartTime_ = helper.GetTimestamp()
	if meta.FirstStartTime_ == 0 {
		meta.FirstStartTime_ = meta.StartTime_
	}

	// 构建上游请求
	upstreamURL := fmt.Sprintf("%s/v1/responses", meta.BaseURL)
	upstreamReq, err := http.NewRequest(http.MethodPost, upstreamURL, bytes.NewReader(requestBody))
	if err != nil {
		return openai.ErrorWrapper(err, "create_request_failed", http.StatusInternalServerError), false, *meta
	}

	// 复制请求头
	copyHeaders(c, upstreamReq)

	// 记录请求结束时间
	meta.RequestEndTime_ = helper.GetTimestamp()

	// 发送请求
	resp, err := client.HTTPClient.Do(upstreamReq)
	if err != nil {
		// 请求失败，退还预扣费
		model.RecordSysLogToDBAndFile(ctx, c.GetString(helper.RequestIdKey), model.LogTypeSystemErr, meta.UserId, meta.ChannelId, meta.RequestModel, meta.TokenName, meta.ChannelName, fmt.Sprintf("respErr is not nil: %+v", err), meta.DetailPrompt)
		if !config.ResponseErrorStillChargeEnabled {
			// 不计费,原路退回
			if meta.IsPackagePlan {
				billing.ReturnPreConsumedPackagePlanQuota(ctx, preConsumedQuota, meta)
			} else {
				billing.ReturnPreConsumedQuota(ctx, preConsumedQuota, meta.TokenId)
			}
			return openai.ErrorWrapper(err, "request_failed", http.StatusInternalServerError), false, *meta
		}
	}
	defer resp.Body.Close()

	// 记录响应首字节时间
	meta.ResponseFirstByteTime_ = helper.GetTimestamp()

	// 通知首字节已收到（用于首字节超时检查）
	if wrapper, exists := c.Get("timeout_wrapper"); exists {
		if timeoutWrapper, ok := wrapper.(*middleware.TimeoutWrapper); ok {
			timeoutWrapper.OnFirstByteReceived()
		}
	}

	// 处理响应
	var result *relayModel.ErrorWithStatusCode
	var usage *relayModel.Usage

	var detailCompletion string
	if request.Stream {
		result, usage, detailCompletion = handleStreamResponseWithUsage(c, resp, meta)
	} else {
		result, usage, detailCompletion = handleNormalResponseWithUsage(c, resp, meta)
	}

	// 设置响应内容用于详细日志记录
	meta.DetailCompletion = detailCompletion

	if usage != nil {
		meta.PromptTokens = usage.PromptTokens
		meta.CompletionTokens = usage.CompletionTokens
	}

	// 后续计费
	helper.SafeGoroutine(func() {
		postConsumeQuota(ctx, usage, meta, request, ratio, preConsumedQuota, modelRatio, groupRatio, topupConvertRatio, userDiscount, c)
	})

	return result, false, *meta
}

// RelayGetResponseHelper 处理 GET /v1/responses/{response_id} 请求
func RelayGetResponseHelper(c *gin.Context) (*relayModel.ErrorWithStatusCode, bool, meta.Meta) {
	meta := meta.GetByContext(c)

	// 从路径中获取 response_id
	responseId := c.Param("id")
	if responseId == "" {
		return openai.ErrorWrapper(fmt.Errorf("missing response id"), "invalid_request", http.StatusBadRequest), false, *meta
	}

	// 构建上游请求 URL
	upstreamURL := fmt.Sprintf("%s/v1/responses/%s", meta.BaseURL, responseId)

	// 添加 include 参数
	if includes := c.QueryArray("include"); len(includes) > 0 {
		query := url.Values{}
		for _, include := range includes {
			query.Add("include", include)
		}
		upstreamURL += "?" + query.Encode()
	}

	// 创建请求
	upstreamReq, err := http.NewRequest(http.MethodGet, upstreamURL, nil)
	if err != nil {
		return openai.ErrorWrapper(err, "create_request_failed", http.StatusInternalServerError), false, *meta
	}

	// 复制请求头
	copyHeaders(c, upstreamReq)

	// 发送请求
	resp, err := client.HTTPClient.Do(upstreamReq)
	if err != nil {
		return openai.ErrorWrapper(err, "request_failed", http.StatusInternalServerError), false, *meta
	}
	defer resp.Body.Close()

	// 处理响应
	result, _, _ := handleNormalResponseWithUsage(c, resp, meta)
	return result, false, *meta
}

// copyHeaders 复制请求头并添加必要的认证信息
func copyHeaders(c *gin.Context, req *http.Request) {
	// 复制原始请求的所有头部
	for key, values := range c.Request.Header {
		// 跳过一些不需要复制的头部
		if key == "Connection" || key == "Content-Length" || key == "Accept-Encoding" {
			continue
		}
		for _, value := range values {
			req.Header.Add(key, value)
		}
	}

	// 设置必要的头部
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Accept-Encoding", "gzip, deflate") // 明确指定接受的压缩格式

	if auth := c.GetHeader("Authorization"); auth != "" {
		req.Header.Set("Authorization", auth)
	}
	if apiKey := c.GetHeader("Api-Key"); apiKey != "" {
		req.Header.Set("Api-Key", apiKey)
	}

	// 设置 User-Agent
	req.Header.Set("User-Agent", "one-api")
}

// 辅助函数：处理流式响应并提取使用量
func handleStreamResponseWithUsage(c *gin.Context, resp *http.Response, meta *meta.Meta) (*relayModel.ErrorWithStatusCode, *relayModel.Usage, string) {
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return openai.ErrorWrapper(fmt.Errorf(string(body)), "upstream_error", resp.StatusCode), nil, ""
	}

	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	// 创建一个缓冲区来存储响应
	var buffer bytes.Buffer
	tee := io.TeeReader(resp.Body, &buffer)

	// 直接转发流式响应
	_, err := io.Copy(c.Writer, tee)
	if err != nil {
		logger.SysError(fmt.Sprintf("stream response failed: %s", err.Error()))
	}

	// 从缓冲区中提取使用量和响应内容
	usage := extractUsageFromStream(buffer.Bytes())
	detailCompletion := extractContentFromStream(buffer.Bytes())

	// 只在需要时记录上游响应和完整响应（考虑用户级别配置）
	if shouldLogUpstreamResponse(meta.UserId) {
		meta.UpstreamResponse = buffer.String()
	}
	if shouldLogFullResponse(meta.UserId) {
		meta.FullResponse = buffer.String()
	}

	return nil, usage, detailCompletion
}

// 辅助函数：处理普通响应并提取使用量
func handleNormalResponseWithUsage(c *gin.Context, resp *http.Response, meta *meta.Meta) (*relayModel.ErrorWithStatusCode, *relayModel.Usage, string) {
	var reader io.Reader = resp.Body

	// 处理压缩的响应
	switch resp.Header.Get("Content-Encoding") {
	case "gzip":
		gr, err := gzip.NewReader(resp.Body)
		if err != nil {
			return openai.ErrorWrapper(err, "read_response_failed", http.StatusInternalServerError), nil, ""
		}
		defer gr.Close()
		reader = gr
	case "deflate":
		reader = flate.NewReader(resp.Body)
		defer reader.(io.ReadCloser).Close()
	}

	body, err := io.ReadAll(reader)
	if err != nil {
		return openai.ErrorWrapper(err, "read_response_failed", http.StatusInternalServerError), nil, ""
	}

	// 只在需要时记录上游响应（考虑用户级别配置）
	if shouldLogUpstreamResponse(meta.UserId) {
		meta.UpstreamResponse = string(body)
	}

	if resp.StatusCode != http.StatusOK {
		return openai.ErrorWrapper(fmt.Errorf(string(body)), "upstream_error", resp.StatusCode), nil, ""
	}

	// 设置响应头
	//for k, v := range resp.Header {
	//	c.Header(k, v[0])
	//}

	// 提取使用量和响应内容
	var response map[string]interface{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		// 如果解析失败，仍然返回原始响应
		c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), body)
		return nil, nil, ""
	}

	// 尝试从响应中提取使用量和内容
	usage := extractUsageFromResponse(response)
	detailCompletion := extractContentFromResponse(response)

	// 返回原始响应
	c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), body)

	// 只在需要时记录完整响应（考虑用户级别配置）
	if shouldLogFullResponse(meta.UserId) {
		meta.FullResponse = string(body)
	}

	return nil, usage, detailCompletion
}

// 辅助函数：从流式响应中提取使用量
func extractUsageFromStream(data []byte) *relayModel.Usage {
	// 将数据按行分割
	lines := bytes.Split(data, []byte("\n"))

	for _, line := range lines {
		// 检查是否是 response.completed 事件
		if bytes.HasPrefix(line, []byte("event: response.completed")) {
			// 找到下一行的数据
			for i := 0; i < len(lines); i++ {
				if bytes.HasPrefix(lines[i], []byte("event: response.completed")) && i+1 < len(lines) && bytes.HasPrefix(lines[i+1], []byte("data: ")) {
					eventData := bytes.TrimPrefix(lines[i+1], []byte("data: "))

					var event map[string]interface{}
					if err := json.Unmarshal(eventData, &event); err != nil {
						continue
					}

					// 从 response.completed 事件中提取 usage 信息
					if response, ok := event["response"].(map[string]interface{}); ok {
						if usage, ok := response["usage"].(map[string]interface{}); ok {
							inputTokens := int(0)
							if it, ok := usage["input_tokens"].(float64); ok {
								inputTokens = int(it)
							}

							outputTokens := int(0)
							if ot, ok := usage["output_tokens"].(float64); ok {
								outputTokens = int(ot)
							}

							totalTokens := int(0)
							if tt, ok := usage["total_tokens"].(float64); ok {
								totalTokens = int(tt)
							} else {
								// 如果没有提供 total_tokens，则计算总和
								totalTokens = inputTokens + outputTokens
							}

							return &relayModel.Usage{
								PromptTokens:     inputTokens,
								CompletionTokens: outputTokens,
								TotalTokens:      totalTokens,
							}
						}
					}
				}
			}
		}
	}

	return nil
}

// 辅助函数：从流式响应中提取内容
func extractContentFromStream(data []byte) string {
	// 将数据按行分割
	lines := bytes.Split(data, []byte("\n"))
	var contentBuilder strings.Builder

	for _, line := range lines {
		if bytes.HasPrefix(line, []byte("data: ")) {
			dataStr := string(bytes.TrimPrefix(line, []byte("data: ")))
			dataStr = strings.TrimSpace(dataStr)

			if dataStr != "" && dataStr != "[DONE]" {
				var event map[string]interface{}
				if err := json.Unmarshal([]byte(dataStr), &event); err == nil {
					// 处理 OpenAI /v1/responses API 流式格式
					if eventType, ok := event["type"].(string); ok {
						switch eventType {
						case "response.content_part.added":
							if part, ok := event["part"].(map[string]interface{}); ok {
								if text, ok := part["text"].(string); ok {
									contentBuilder.WriteString(text)
								}
							}
						case "response.content_part.done":
							if part, ok := event["part"].(map[string]interface{}); ok {
								if text, ok := part["text"].(string); ok {
									contentBuilder.WriteString(text)
								}
							}
						case "response.output_item.added":
							// 处理 output_item.added 事件
							if item, ok := event["item"].(map[string]interface{}); ok {
								if content, ok := item["content"].([]interface{}); ok {
									for _, contentItem := range content {
										if contentMap, ok := contentItem.(map[string]interface{}); ok {
											if text, ok := contentMap["text"].(string); ok {
												contentBuilder.WriteString(text)
											}
										}
									}
								}
							}
						case "response.output_item.done":
							// 处理 output_item.done 事件
							if item, ok := event["item"].(map[string]interface{}); ok {
								if content, ok := item["content"].([]interface{}); ok {
									for _, contentItem := range content {
										if contentMap, ok := contentItem.(map[string]interface{}); ok {
											if text, ok := contentMap["text"].(string); ok {
												contentBuilder.WriteString(text)
											}
										}
									}
								}
							}
						}
					}

					// 也尝试从 delta 中提取内容（兼容其他格式）
					if delta, ok := event["delta"].(map[string]interface{}); ok {
						if text, ok := delta["text"].(string); ok {
							contentBuilder.WriteString(text)
						}
					}
				}
			}
		}
	}

	return contentBuilder.String()
}

// 辅助函数：从普通响应中提取使用量
func extractUsageFromResponse(response map[string]interface{}) *relayModel.Usage {
	if usageData, ok := response["usage"].(map[string]interface{}); ok {
		inputTokens := int(0)
		if it, ok := usageData["input_tokens"].(float64); ok {
			inputTokens = int(it)
		}

		outputTokens := int(0)
		if ot, ok := usageData["output_tokens"].(float64); ok {
			outputTokens = int(ot)
		}

		totalTokens := int(0)
		if tt, ok := usageData["total_tokens"].(float64); ok {
			totalTokens = int(tt)
		} else {
			totalTokens = inputTokens + outputTokens
		}

		return &relayModel.Usage{
			PromptTokens:     inputTokens,
			CompletionTokens: outputTokens,
			TotalTokens:      totalTokens,
		}
	}
	return nil
}

// 辅助函数：从普通响应中提取内容
func extractContentFromResponse(response map[string]interface{}) string {
	var contentBuilder strings.Builder

	// 处理 OpenAI /v1/responses API 格式：output[].content[].text
	if output, ok := response["output"].([]interface{}); ok {
		for _, outputItem := range output {
			if outputMap, ok := outputItem.(map[string]interface{}); ok {
				if content, ok := outputMap["content"].([]interface{}); ok {
					for _, contentItem := range content {
						if contentMap, ok := contentItem.(map[string]interface{}); ok {
							if text, ok := contentMap["text"].(string); ok {
								contentBuilder.WriteString(text)
							}
						}
					}
				}
			}
		}
	}

	// 尝试从 content 字段提取（Claude 格式）
	if content, ok := response["content"].([]interface{}); ok {
		for _, item := range content {
			if itemMap, ok := item.(map[string]interface{}); ok {
				if text, ok := itemMap["text"].(string); ok {
					contentBuilder.WriteString(text)
				}
			}
		}
	}

	// 尝试从 message.content 字段提取（其他格式）
	if message, ok := response["message"].(map[string]interface{}); ok {
		if content, ok := message["content"].([]interface{}); ok {
			for _, item := range content {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if text, ok := itemMap["text"].(string); ok {
						contentBuilder.WriteString(text)
					}
				}
			}
		}
	}

	return contentBuilder.String()
}

// 辅助函数：估算请求的 token 数量
func estimateTokensForResponses(request *relayModel.GeneralOpenAIRequest) int {
	// 实现估算 token 数量的逻辑
	// 这可能需要根据输入类型（文本、图像等）进行不同的估算

	// 示例实现：简单地计算文本长度
	var tokenCount int = 0

	// 处理输入
	switch input := request.Input.(type) {
	case string:
		// 文本输入
		tokenCount += len(input) / 4 // 粗略估计：每4个字符约为1个token
	case map[string]interface{}:
		// 复杂输入（如图像）
		if text, ok := input["text"].(string); ok {
			tokenCount += len(text) / 4
		}
	case []interface{}:
		// 数组输入
		for _, item := range input {
			if text, ok := item.(string); ok {
				tokenCount += len(text) / 4
			} else if itemMap, ok := item.(map[string]interface{}); ok {
				if text, ok := itemMap["text"].(string); ok {
					tokenCount += len(text) / 4
				}
			}
		}
	}

	// 添加系统消息的 token
	if request.Instructions != "" {
		tokenCount += len(request.Instructions) / 4
	}

	// 确保至少有一个 token
	if tokenCount < 1 {
		tokenCount = 1
	}

	return tokenCount
}

// shouldLogUpstreamResponse 判断是否应该记录上游响应（考虑用户个性化配置）
func shouldLogUpstreamResponse(userId int) bool {
	return model.ShouldLogUpstreamResponse(userId)
}

// shouldLogFullResponse 判断是否应该记录完整响应（考虑用户个性化配置）
func shouldLogFullResponse(userId int) bool {
	return model.ShouldLogFullResponse(userId)
}

package model

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"

	"gorm.io/gorm"

	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
)

const (
	TokenStatusEnabled   = 1 // don't use 0, 0 is the default value!
	TokenStatusDisabled  = 2 // also don't use 0
	TokenStatusExpired   = 3
	TokenStatusExhausted = 4
)

type Token struct {
	Id             int     `json:"id"`
	UserId         int     `json:"user_id" gorm:"index" `
	Key            string  `json:"key" gorm:"type:char(48);uniqueIndex"`
	Status         int     `json:"status" gorm:"default:1"`
	Name           string  `json:"name" gorm:"index" `
	Group          string  `json:"group" gorm:"type:varchar(32);index"`
	CreatedTime    int64   `json:"created_time" gorm:"bigint"`
	AccessedTime   int64   `json:"accessed_time" gorm:"bigint"`
	ExpiredTime    int64   `json:"expired_time" gorm:"bigint;default:-1"` // -1 means never expired
	RemainQuota    int64   `json:"remain_quota" gorm:"bigint;default:0"`
	UnlimitedQuota bool    `json:"unlimited_quota" gorm:"default:false"`
	UsedQuota      int64   `json:"used_quota" gorm:"bigint;default:0"` // used quota
	Models         string  `json:"models"`
	Subnet         *string `json:"subnet" gorm:"default:''"` // allowed subnet
	IpWhitelist    string  `json:"ip_whitelist"`
	BillingType    int     `json:"billing_type" gorm:"default:1;index"`     // 计费类型: 1 - 按量计费, 2 - 按次计费
	IsInitialToken bool    `json:"is_initial_token" gorm:"default:0;index"` // 是否为初始令牌,1是,默认0否
	Remark         string  `json:"remark"`                                  //令牌备注
	Advertisement  string  `json:"advertisement"`                           //广告语
	AdPosition     int     `json:"ad_position" gorm:"default:0"`            // 插入广告位置0或-1.不插入.1.开头.2.结尾.3.随机
}

func GetAllUserTokens(userId int, startIdx int, num int, order string, name string, status int, group string, billingType int, key string) ([]*Token, error) {
	var tokens []*Token
	var err error
	groupCol := "`group`"
	keyCol := "`key`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
		keyCol = `"key"`
	}
	tx := DB
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if group != "" {
		// 模糊
		tx = tx.Where(groupCol+" LIKE ?", "%"+group+"%")
	}
	if billingType != 0 {
		tx = tx.Where("billing_type = ?", billingType)
	}
	if key != "" {
		tx = tx.Where(keyCol+" = ?", key)
	}
	tx = tx.Where("user_id = ?", userId)
	switch order {
	case "id":
		tx = tx.Order("id desc")
	case "name":
		tx = tx.Order("name desc")
	case "status":
		tx = tx.Order("status desc")
	case "created_time":
		tx = tx.Order("created_time desc")
	default:
		tx = tx.Order("id desc")
	}
	err = tx.Limit(num).Offset(startIdx).Find(&tokens).Error
	return tokens, err
}

func CountTokens(userId int, name string, status int, group string, billingType int, key string) (count int64, err error) {
	groupCol := "`group`"
	keyCol := "`key`"
	if common.UsingPostgreSQL {
		groupCol = `"group"`
		keyCol = `"key"`
	}
	tx := DB.Model(&Token{})
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}
	if status != 0 {
		tx = tx.Where("status = ?", status)
	}
	if group != "" {
		// 模糊
		tx = tx.Where(groupCol+" LIKE ?", "%"+group+"%")
	}
	if billingType != 0 {
		tx = tx.Where("billing_type = ?", billingType)
	}
	if key != "" {
		tx = tx.Where(keyCol+" = ?", key)
	}
	err = tx.Where("user_id = ?", userId).Count(&count).Error
	return count, err
}

func SearchUserTokens(userId int, keyword string, startIdx int, num int) (tokens []*Token, err error) {
	keyCol := "`key`"
	if common.UsingPostgreSQL {
		keyCol = `"key"`
	}
	tx := DB
	// 如果是sk-开头就直接查key
	if keyword != "" && len(keyword) > 3 && keyword[:3] == "sk-" {
		tx = tx.Where("user_id = ?", userId).Where(keyCol+" = ?", keyword[3:])
	} else {
		tx = tx.Where("user_id = ?", userId).Where("name LIKE ?", "%"+keyword+"%")
	}
	// 排序
	tx = tx.Order("id desc")
	if num != 0 {
		tx = tx.Limit(num).Offset(startIdx)
	}
	err = tx.Find(&tokens).Error
	return tokens, err
}

func GetUserTokensByName(name string) (tokens []*Token, err error) {
	err = DB.Where("name = ?", name).Find(&tokens).Error
	return tokens, err
}

func GetUserInitialToken(userId int) (token *Token, err error) {
	err = DB.Where("is_initial_token = true and user_id = ?", userId).Find(&token).Error
	return token, err
}

func RefreshInitialToken(userId int) (key string, err error) {
	tx := DB
	refreshKey := helper.GenerateKey()
	err = tx.Model(&Token{}).Where("is_initial_token = 1 and user_id = ?", userId).Updates(Token{
		Key: refreshKey,
	}).Error
	return refreshKey, err
}

func ValidateUserToken2(c *gin.Context, key string, jwtUserInitialToken string, isJwtToken bool) (token *Token, tokenExtend *TokenExtend, err error) {
	if key == "" {
		return nil, nil, errors.New("未提供令牌")
	}
	token, tokenExtend, err = CacheGetTokenByKey(key)
	c.Set(ctxkey.TokenKey, key)
	if err != nil {
		logger.SysError(fmt.Sprintf("CacheGetTokenByKey failed with key==>%s: ", key) + err.Error())
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, errors.New(fmt.Sprintf("[%s]无效的令牌", key))
		}
		// key需要做敏感屏蔽,替换掉开头几位,只保留尾号
		return nil, nil, errors.New(fmt.Sprintf("[%s]令牌验证失败,初始令牌[%s],是否为jwt[%t] => %s",
			helper.MaskMiddle(key, 5, 5, '*'),
			helper.MaskMiddle(jwtUserInitialToken, 5, 5, '*'), isJwtToken, err.Error()))
	}
	c.Set(ctxkey.TokenName, token.Name)
	c.Set(ctxkey.TokenGroup, token.Group)
	if token.Status == TokenStatusExhausted {
		return nil, nil, errors.New("该令牌额度已用尽")
	} else if token.Status == common.TokenStatusExpired {
		return nil, nil, errors.New("该令牌已过期")
	}
	if token.Status != TokenStatusEnabled {
		return nil, nil, errors.New("该令牌状态不可用")
	}
	if token.ExpiredTime != -1 && token.ExpiredTime < helper.GetTimestamp() {
		if !common.RedisEnabled {
			token.Status = TokenStatusExpired
			err := token.SelectUpdate()
			if err != nil {
				logger.SysError("failed to update token status" + err.Error())
			}
		}
		return nil, nil, errors.New("该令牌已过期")
	}
	if !token.UnlimitedQuota && token.RemainQuota <= 0 {
		if !common.RedisEnabled {
			// in this case, we can make sure the token is exhausted
			token.Status = TokenStatusExhausted
			err := token.SelectUpdate()
			if err != nil {
				logger.SysError("failed to update token status" + err.Error())
			}
		}
		return nil, nil, errors.New("该令牌额度已用尽")
	}
	return token, tokenExtend, nil
}

func ValidateUserToken(key string) (token *Token, tokenExtend *TokenExtend, err error) {
	if key == "" {
		return nil, nil, errors.New("未提供令牌")
	}
	token, tokenExtend, err = CacheGetTokenByKey(key)
	if err != nil {
		logger.SysError(fmt.Sprintf("CacheGetTokenByKey failed with key==>%s: ", key) + err.Error())
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, errors.New(fmt.Sprintf("[%s]无效的令牌", key))
		}
		// key需要做敏感屏蔽,替换掉开头几位,只保留尾号
		return nil, nil, errors.New(fmt.Sprintf("[%s]令牌验证失败 => %s", helper.MaskMiddle(key, 5, 5, '*'), err.Error()))
	}
	if token.Status == TokenStatusExhausted {
		return nil, nil, errors.New("该令牌额度已用尽")
	} else if token.Status == common.TokenStatusExpired {
		return nil, nil, errors.New("该令牌已过期")
	}
	if token.Status != TokenStatusEnabled {
		return nil, nil, errors.New("该令牌状态不可用")
	}
	if token.ExpiredTime != -1 && token.ExpiredTime < helper.GetTimestamp() {
		if !common.RedisEnabled {
			token.Status = TokenStatusExpired
			err := token.SelectUpdate()
			if err != nil {
				logger.SysError("failed to update token status" + err.Error())
			}
		}
		return nil, nil, errors.New("该令牌已过期")
	}
	if !token.UnlimitedQuota && token.RemainQuota <= 0 {
		if !common.RedisEnabled {
			// in this case, we can make sure the token is exhausted
			token.Status = TokenStatusExhausted
			err := token.SelectUpdate()
			if err != nil {
				logger.SysError("failed to update token status" + err.Error())
			}
		}
		return nil, nil, errors.New("该令牌额度已用尽")
	}
	return token, tokenExtend, nil
}

func GetTokenByIds(id int, userId int) (*Token, error) {
	if id == 0 || userId == 0 {
		return nil, errors.New("id 或 userId 为空！")
	}
	token := Token{Id: id, UserId: userId}
	var err error = nil
	err = DB.First(&token, "id = ? and user_id = ?", id, userId).Error
	return &token, err
}

func GetTokenByKey(key string) (*Token, error) {
	token := Token{Key: key}
	var err error = nil
	// 去掉sk-
	if len(key) >= 3 && key[:3] == "sk-" {
		key = key[3:]
	} else {
		return nil, errors.New("令牌长度不正确！")
	}
	err = DB.First(&token, "`key` = ? ", key).Error
	return &token, err
}

func GetTokenById(id int) (*Token, error) {
	if id == 0 {
		return nil, errors.New("id 为空！")
	}
	token := Token{Id: id}
	var err error = nil
	err = DB.First(&token, "id = ?", id).Error
	return &token, err
}

func (token *Token) Insert() error {
	var err error
	if err := ValidateGroupName(token.Group); err != nil {
		return err
	}
	// 验证用户对分组的访问权限
	if err := ValidateUserGroupAccess(token.UserId, token.Group); err != nil {
		return err
	}
	err = DB.Create(token).Error
	return err
}

func (token *Token) InsertByTx(tx *gorm.DB) error {
	var err error
	if err := ValidateGroupName(token.Group); err != nil {
		return err
	}
	// 验证用户对分组的访问权限
	if err := ValidateUserGroupAccess(token.UserId, token.Group); err != nil {
		return err
	}
	err = tx.Create(token).Error
	return err
}

// Update Make sure your token's fields is completed, because this will update non-zero values
func (token *Token) Update() error {
	var err error
	if err := ValidateGroupName(token.Group); err != nil {
		return err
	}
	// 验证用户对分组的访问权限
	if err := ValidateUserGroupAccess(token.UserId, token.Group); err != nil {
		return err
	}
	err = DB.Model(token).Select("name", "status", "expired_time", "remain_quota", "unlimited_quota", "models", "subnet", "ip_whitelist", "billing_type", "advertisement", "ad_position", "remark", "group").Updates(token).Error

	// 更新成功后清除相关缓存
	if err == nil {
		DeleteTokenByIdCache(token.Id)
	}

	return err
}

func (token *Token) UpdateByTx(tx *gorm.DB) error {
	var err error
	if err := ValidateGroupName(token.Group); err != nil {
		return err
	}
	// 验证用户对分组的访问权限
	if err := ValidateUserGroupAccess(token.UserId, token.Group); err != nil {
		return err
	}
	err = tx.Model(token).Select("name", "status", "expired_time", "remain_quota", "unlimited_quota", "models", "ip_whitelist", "billing_type", "advertisement", "ad_position", "remark", "group").Updates(token).Error

	// 更新成功后清除相关缓存
	if err == nil {
		DeleteTokenByIdCache(token.Id)
	}

	return err
}

func (t *Token) SelectUpdate() error {
	// This can update zero values
	return DB.Model(t).Select("accessed_time", "status").Updates(t).Error
}

func (t *Token) Delete() error {
	var err error
	err = DB.Delete(t).Error

	// 删除成功后清除相关缓存
	if err == nil {
		DeleteTokenByIdCache(t.Id)
	}

	return err
}

func (t *Token) GetModels() string {
	if t == nil {
		return ""
	}
	if t.Models == "" {
		return ""
	}
	return t.Models
}

func DeleteTokenById(id int, userId int) (err error) {
	// Why we need userId here? In case user want to delete other's token.
	if id == 0 || userId == 0 {
		return errors.New("id 或 userId 为空！")
	}
	token := Token{Id: id, UserId: userId}
	err = DB.Where(token).First(&token).Error
	if err != nil {
		return err
	}
	return token.Delete()
}

func DeleteTokenByIds(ids []int, userId int) (row int64, err error) {
	if len(ids) == 0 || userId == 0 {
		return 0, errors.New("ids 或 userId 为空！")
	}
	var token Token
	// 不能删除初始令牌,最后要返回删除的个数
	result := DB.Where("id in (?) and user_id = ? and is_initial_token = 0", ids, userId).Delete(&token)
	return result.RowsAffected, result.Error
}

func TopupTokenQuota(id int, quota int64) (err error) {
	err = DB.Model(&Token{}).Where("id = ?", id).Updates(
		map[string]interface{}{
			"remain_quota":  gorm.Expr("remain_quota + ?", quota),
			"accessed_time": helper.GetTimestamp(),
		},
	).Error

	// 更新成功后清除相关缓存
	if err == nil {
		DeleteTokenByIdCache(id)
	}

	return err
}

func IncreaseTokenQuota(id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeTokenQuota, id, quota)
		return nil
	}
	return increaseTokenQuota(id, quota)
}

func increaseTokenQuota(id int, quota int64) (err error) {
	err = DB.Model(&Token{}).Where("id = ?", id).Updates(
		map[string]interface{}{
			"remain_quota":  gorm.Expr("remain_quota + ?", quota),
			"used_quota":    gorm.Expr("used_quota - ?", quota),
			"accessed_time": helper.GetTimestamp(),
		},
	).Error
	return err
}

func DecreaseTokenQuota(id int, quota int64) (err error) {
	if quota < 0 {
		return errors.New("quota 不能为负数！")
	}
	if config.BatchUpdateEnabled {
		addNewRecord(BatchUpdateTypeTokenQuota, id, -quota)
		return nil
	}
	return decreaseTokenQuota(id, quota)
}

func decreaseTokenQuota(id int, quota int64) (err error) {
	err = DB.Model(&Token{}).Where("id = ?", id).Updates(
		map[string]interface{}{
			"remain_quota":  gorm.Expr("remain_quota - ?", quota),
			"used_quota":    gorm.Expr("used_quota + ?", quota),
			"accessed_time": helper.GetTimestamp(),
		},
	).Error
	return err
}

func PreConsumeTokenQuota(ctx context.Context, tokenId int, quota int64) (err error, isLowQuota bool) {
	isLowQuota = false
	if quota < 0 {
		return errors.New("quota 不能为负数！"), isLowQuota
	}
	// 使用缓存版本的Token查询，避免频繁查询数据库
	token, err := CacheGetTokenById(tokenId)
	if err != nil {
		return err, isLowQuota
	}
	if !token.UnlimitedQuota && token.RemainQuota < quota {
		return errors.New(fmt.Sprintf("令牌[%s]额度不足", helper.MaskMiddle(token.Key, 5, 5, '*'))), isLowQuota
	}
	userQuota, err := CacheGetUserQuota(ctx, token.UserId)
	if err != nil {
		return err, isLowQuota
	}
	if userQuota < quota {
		return errors.New(fmt.Sprintf("用户[%d]额度不足", token.UserId)), isLowQuota
	}

	// 获取用户的通知设置，使用用户自定义的余额阈值
	var quotaThreshold int64 = config.QuotaRemindThreshold // 默认使用系统配置
	if notificationSetting, err := CacheGetUserNotificationSetting(token.UserId); err == nil {
		// 将用户设置的美元金额转换为系统内部的quota单位（1美元 = 500000）
		quotaThreshold = int64(notificationSetting.BalanceThreshold * 500000)
	}

	quotaTooLow := userQuota-quota < quotaThreshold
	noMoreQuota := userQuota-quota <= 0
	if quotaTooLow || noMoreQuota {
		isLowQuota = true
	}
	err = DecreaseTokenQuota(tokenId, quota)
	if err != nil {
		return err, isLowQuota
	}
	err = DecreaseUserQuotaAndRedis(token.UserId, quota)
	return err, isLowQuota
}

func PostConsumeTokenQuota(tokenId int, quota int64) (err error) {
	token, err := CacheGetTokenById(tokenId)
	if err != nil {
		return err
	}
	if quota > 0 {
		err = DecreaseUserQuotaAndRedis(token.UserId, quota)
	} else {
		err = IncreaseUserQuotaAndRedis(token.UserId, -quota)
	}
	if err != nil {
		return err
	}
	if quota > 0 {
		err = DecreaseTokenQuota(tokenId, quota)
	} else {
		err = IncreaseTokenQuota(tokenId, -quota)
	}
	if err != nil {
		return err
	}
	return nil
}

func ValidateGroupName(groupName string) error {
	if groupName == "" {
		return nil
	}
	var count int64
	err := DB.Model(&Group{}).Where("name = ?", groupName).Count(&count).Error
	if err != nil {
		return err
	}
	if count == 0 {
		return errors.New("invalid group name")
	}
	return nil
}

// ValidateUserGroupAccess 验证用户对指定分组的访问权限
func ValidateUserGroupAccess(userId int, groupName string) error {
	if groupName == "" {
		return nil
	}

	// 获取用户信息
	user, err := CacheGetUserById(userId, false)
	if err != nil {
		return errors.New("用户不存在")
	}
	if user == nil {
		return errors.New("用户不存在")
	}

	// 检查分组是否存在
	group, err := CacheGetGroupByName(groupName)
	if err != nil {
		return errors.New("分组不存在")
	}
	if group == nil {
		return errors.New("分组不存在")
	}

	// 判断是否为管理员
	isAdmin := IsAdmin(userId)

	// 管理员可以访问所有可选分组
	if isAdmin {
		if !group.GetIsSelectable() {
			return errors.New("分组不存在")
		}
		return nil
	}

	// 普通用户需要检查分组的可见性和可选性
	if !group.GetIsSelectable() {
		return errors.New("分组不存在")
	}

	// 检查分组是否对普通用户可见
	if group.IsVisible == nil || !*group.IsVisible {
		return errors.New("分组不存在")
	}

	// 获取用户当前分组信息，检查倍率限制
	userGroup, err := CacheGetGroupByName(user.Group)
	if err != nil {
		return errors.New("获取用户分组信息失败")
	}

	// 如果当前用户是0倍率分组，但是请求的分组是有倍率的，那么就不允许
	if (userGroup.GroupRatio == 0 || userGroup.TopupGroupRatio == 0) &&
		(group.GroupRatio != 0 && group.TopupGroupRatio != 0) {
		return errors.New("分组不存在")
	}

	// 检查用户额外可见分组
	extraGroups, err := GetUserExtraVisibleGroupsByUserId(userId)
	if err == nil && extraGroups != "" {
		extraGroupsList := strings.Split(extraGroups, ",")
		for _, extraGroup := range extraGroupsList {
			if strings.TrimSpace(extraGroup) == groupName {
				return nil // 在额外可见分组中找到，允许访问
			}
		}
	}

	return nil
}

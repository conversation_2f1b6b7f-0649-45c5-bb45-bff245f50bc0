fix: 修复超时处理逻辑，区分流式和非流式请求的超时策略

问题描述：
- 非流式请求被首字节超时（15秒）提前截断，而不是使用总超时（45秒）
- 流式和非流式请求应该有不同的超时处理策略

解决方案：
1. 修改超时中间件逻辑：
   - 流式请求：设置首字节超时(15s) + 总超时(45s)
   - 非流式请求：只设置总超时(45s)，不设置首字节超时

2. 修复首字节通知机制：
   - relay/controller/responses.go: 添加首字节通知
   - relay/adaptor/openai/main.go: 修复HandlerWithResponse函数
   - relay/adaptor/anthropic/main.go: 修复多个处理函数和流式处理位置

3. 新增功能：
   - 添加isStreamRequest()函数判断请求类型
   - 优化日志输出，区分流式和非流式请求的超时配置

影响：
- 非流式请求现在可以使用完整的45秒总超时时间
- 流式请求保持原有的首字节+总超时双重保护
- 提升了长时间处理请求的用户体验
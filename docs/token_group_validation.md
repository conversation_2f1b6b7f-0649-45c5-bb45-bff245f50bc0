# 令牌分组权限验证功能

## 功能说明

为了防止恶意用户通过猜测分组ID进行注入攻击，系统在用户保存令牌分组时会验证该分组对当前用户是否可见和可用。

## 实现细节

### 新增验证函数

在 `model/token.go` 中新增了 `ValidateUserGroupAccess` 函数，用于验证用户对指定分组的访问权限：

```go
func ValidateUserGroupAccess(userId int, groupName string) error
```

### 验证逻辑

1. **空分组名**：允许通过（表示不指定分组）
2. **用户存在性**：验证用户是否存在
3. **分组存在性**：验证分组是否存在
4. **管理员权限**：管理员可以访问所有可选分组
5. **普通用户权限**：
   - 分组必须是可选的（`is_selectable = true`）
   - 分组必须是可见的（`is_visible = true`）
   - 检查倍率限制（0倍率用户不能使用有倍率的分组）
   - 检查用户额外可见分组列表

### 集成点

验证逻辑已集成到以下方法中：
- `Token.Insert()`
- `Token.InsertByTx()`
- `Token.Update()`
- `Token.UpdateByTx()`

## 错误处理

当用户尝试访问无权限的分组时，系统会返回统一的错误信息："分组不存在"，避免泄露分组的实际存在状态。

## 测试建议

### 手动测试场景

1. **普通用户尝试使用不可见分组**
   ```bash
   # 通过API创建令牌时指定不可见的分组名
   curl -X POST /api/token \
     -H "Authorization: Bearer <user_token>" \
     -d '{"name": "test", "group": "hidden_group"}'
   # 预期结果：返回"分组不存在"错误
   ```

2. **普通用户尝试使用不可选分组**
   ```bash
   # 通过API创建令牌时指定不可选的分组名
   curl -X POST /api/token \
     -H "Authorization: Bearer <user_token>" \
     -d '{"name": "test", "group": "non_selectable_group"}'
   # 预期结果：返回"分组不存在"错误
   ```

3. **管理员使用任意可选分组**
   ```bash
   # 管理员应该能够使用任何可选分组
   curl -X POST /api/token \
     -H "Authorization: Bearer <admin_token>" \
     -d '{"name": "test", "group": "any_selectable_group"}'
   # 预期结果：成功创建令牌
   ```

4. **0倍率用户尝试使用有倍率分组**
   ```bash
   # 0倍率用户尝试使用有倍率的分组
   curl -X POST /api/token \
     -H "Authorization: Bearer <zero_ratio_user_token>" \
     -d '{"name": "test", "group": "high_ratio_group"}'
   # 预期结果：返回"分组不存在"错误
   ```

### 自动化测试

可以使用 `model/token_group_validation_test.go` 中的测试框架进行自动化测试。

## 安全考虑

1. **统一错误信息**：所有权限相关的错误都返回"分组不存在"，避免信息泄露
2. **权限分离**：管理员和普通用户有不同的权限验证逻辑
3. **倍率限制**：防止0倍率用户滥用有倍率的分组
4. **额外可见分组**：支持为特定用户配置额外的可见分组

## 向后兼容性

- 现有的 `ValidateGroupName` 函数保持不变，确保基本的分组存在性验证
- 新的权限验证是额外的安全层，不会影响现有功能
- 空分组名仍然被允许，保持现有行为

## 实际使用示例

### 场景1：普通用户尝试恶意注入分组

**请求**：
```bash
curl -X POST /api/token \
  -H "Authorization: Bearer user_token" \
  -d '{"name": "malicious", "group": "admin-only-group"}'
```

**响应**：
```json
{
  "success": false,
  "message": "分组不存在"
}
```

### 场景2：管理员正常创建令牌

**请求**：
```bash
curl -X POST /api/token \
  -H "Authorization: Bearer admin_token" \
  -d '{"name": "admin-token", "group": "premium-group"}'
```

**响应**：
```json
{
  "success": true,
  "message": "",
  "data": {
    "id": 123,
    "name": "admin-token",
    "group": "premium-group",
    ...
  }
}
```

### 场景3：普通用户使用额外可见分组

如果用户在 `extra_visible_groups` 中配置了 "special-group"：

**请求**：
```bash
curl -X POST /api/token \
  -H "Authorization: Bearer user_token" \
  -d '{"name": "special", "group": "special-group"}'
```

**响应**：
```json
{
  "success": true,
  "message": "",
  "data": {
    "id": 124,
    "name": "special",
    "group": "special-group",
    ...
  }
}
```

## 部署建议

1. **逐步部署**：建议先在测试环境验证功能正常
2. **监控日志**：关注是否有大量"分组不存在"错误，可能表示有恶意尝试
3. **用户通知**：如果有用户反馈无法使用某些分组，检查分组的可见性和可选性设置
4. **性能考虑**：新增的验证逻辑会增加数据库查询，但影响很小

## 故障排除

### 问题：用户无法创建令牌，提示"分组不存在"

**排查步骤**：
1. 检查分组是否存在：`SELECT * FROM groups WHERE name = 'group_name'`
2. 检查分组是否可选：`is_selectable = 1`
3. 检查分组是否可见：`is_visible = 1`（仅普通用户）
4. 检查用户倍率限制
5. 检查用户额外可见分组配置

### 问题：管理员也无法使用某些分组

**排查步骤**：
1. 确认用户确实是管理员角色
2. 检查分组的 `is_selectable` 字段，管理员也不能使用不可选分组
